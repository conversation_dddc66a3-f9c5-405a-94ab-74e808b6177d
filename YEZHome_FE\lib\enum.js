import AtmIcon from "@/app/icon/atm-icon";
import BankingIcon from "@/app/icon/banking-icon";
import CreditCardIcon from "@/app/icon/creditcard-icon";
import MomoIcon from "@/app/icon/momo-icon";
import VnPayIcon from "@/app/icon/vnpay-icon";

export const FormType = Object.freeze({
  EDIT: "Edit",
  NEW: "New"
});

// PropertyStatus Enum
export const PropertyStatus = Object.freeze({
  DRAFT: "Draft",
  PENDING_APPROVAL: "PendingApproval",
  APPROVED: "Approved",
  REJECTED_BY_ADMIN: "RejectedByAdmin",
  REJECTED_DUE_TO_UNPAID: "RejectedDueToUnpaid",
  WAITING_PAYMENT: "WaitingPayment",
  EXPIRED: "Expired",
  SOLD: "Sold"
});

// PropertyType Enum
export const PropertyType = Object.freeze({
  SALE: "Sale",
  RENT: "Rent"
});

export const DEFAULT_ITEM_PER_PAGE = 10;
export const DEFAULT_PAGE = 1;
export const DEFAULT_POST_PRICE = 55000;

// Enum for member ranks
export const MemberRank = Object.freeze({
  DIAMOND: "diamond",
  PLATINUM: "platinum",
  GOLD: "gold",
  SILVER: "silver",
  BRONZE: "bronze",
  DEFAULT: "default"
});

// Highlight prices based on member rank
export const highlightPrices = Object.freeze({
  [MemberRank.DIAMOND]: 30000,
  [MemberRank.PLATINUM]: 35000,
  [MemberRank.GOLD]: 40000,
  [MemberRank.SILVER]: 45000,
  [MemberRank.BRONZE]: 50000,
  [MemberRank.DEFAULT]: 55000,
});


// Default coordinates for Ho Chi Minh City
export const HCM_COORDINATES_DISTRICT_2 = Object.freeze({
  latitude: 10.79, // Tọa độ ví dụ cho Quận 2 (nay thuộc TP Thủ Đức)
  longitude: 106.73,
  accuracy: 0 // Vị trí mặc định không có dữ liệu độ chính xác
});

export const CAN_NOT_EDIT_STATUS = [
    PropertyStatus.PENDING_APPROVAL,
    PropertyStatus.APPROVED,
    PropertyStatus.SOLD,
    PropertyStatus.EXPIRED,
    PropertyStatus.WAITING_PAYMENT,
  ];

export const USER_TYPE = Object.freeze({
  SELLER: "Seller",
  BUYER: "Buyer"
});

export const NOTIFICATION_TYPE = Object.freeze({
  SYSTEM: "system",
  TRANSACTION: "transaction",
  PROMOTION: "promotion",
  CONTACT: "contact",
  WALLET_UPDATE: "wallet_update",
  NEWS: "news",
  CUSTOMER_MESSAGE: "customer_message",
});

export const TRANSACTION_TYPE = Object.freeze({
  TOP_UP: "TOP_UP",
  PAYMENT_POST: "PAYMENT_POST",
  PAYMENT_HIGHLIGHT: "PAYMENT_HIGHLIGHT",
});

export const TRANSACTION_STATUS = Object.freeze({
  COMPLETED: "COMPLETED",
  PENDING: "PENDING",
  FAILED: "FAILED",
  CANCELLED: "CANCELLED",
});

export const PAYMENT_METHODS = [
  {
    id: "banking",
    name: "Banking Transfer",
    icon: <BankingIcon className="h-6 w-6" />,
    description: "Transfer directly from your bank account",
  },
  {
    id: "momo",
    name: "MoMo Pay",
    icon: <MomoIcon className="h-6 w-6" />,
    description: "Pay with your MoMo wallet",
  },
  {
    id: "credit",
    name: "Credit Card",
    icon: <CreditCardIcon className="h-6 w-6" />,
    description: "Visa, Mastercard, JCB",
  },
  {
    id: "atm",
    name: "ATM Card",
    icon: <AtmIcon className="h-6 w-6" />,
    description: "Domestic ATM cards",
  },
  {
    id: "vnpay",
    name: "VNPay QR",
    icon: <VnPayIcon className="h-6 w-6" />,
    description: "Scan QR code to pay",
  },
]

export const PRESET_AMOUNTS = [
  { value: 100000, label: "100,000₫" },
  { value: 200000, label: "200,000₫" },
  { value: 500000, label: "500,000₫" },
  { value: 1000000, label: "1,000,000₫" },
  { value: 2000000, label: "2,000,000₫" },
  { value: 3000000, label: "3,000,000₫" },
];